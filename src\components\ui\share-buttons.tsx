"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Facebook,
  Twitter,
  Linkedin,
  MessageCircle,
  Copy
} from "lucide-react";

// Custom Pinterest and Reddit icons as SVG components
const PinterestIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 0C5.373 0 0 5.372 0 12c0 5.084 3.163 9.426 7.627 11.174-.105-.949-.2-2.405.042-3.441.219-.937 1.407-5.965 1.407-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738a.36.36 0 01.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.888-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.357-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24 12.001 24c6.624 0 11.999-5.373 11.999-12C24 5.372 18.626.001 12.001.001z"/>
  </svg>
);

const RedditIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.************* 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"/>
  </svg>
);
import { cn } from "@/lib/utils";

interface ShareButtonsProps {
  url?: string;
  title?: string;
  description?: string;
  className?: string;
  size?: "default" | "sm" | "lg" | "icon";
  variant?: "default" | "outline" | "ghost";
  showLabels?: boolean;
}

export default function ShareButtons({
  url = "https://bratgenerator.casa", // 默认使用网站URL，避免window依赖
  title = "Check out this amazing Brat Generator!",
  description = "Create stunning brat meme images with our free online brat generator tool.",
  className,
  size = "default",
  variant = "outline",
  showLabels = false
}: ShareButtonsProps) {
  const [copied, setCopied] = useState(false);

  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
    pinterest: `https://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedTitle}`,
    reddit: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`,
  };

  const handleShare = (platform: keyof typeof shareLinks) => {
    window.open(shareLinks[platform], '_blank', 'width=600,height=400');
  };

  const handleCopyLink = async () => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(url);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const buttonSize = {
    default: "h-9 w-9",
    sm: "h-8 w-8",
    lg: "h-12 w-12",
    icon: "h-9 w-9"
  };

  const iconSize = {
    default: "h-4 w-4",
    sm: "h-4 w-4",
    lg: "h-6 w-6",
    icon: "h-4 w-4"
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>

      {/* Facebook */}
      <Button
        variant={variant}
        size="icon"
        className={cn(buttonSize[size], "text-[#1877F2] hover:bg-[#1877F2]/10 border-[#1877F2]/20")}
        onClick={() => handleShare('facebook')}
        title="Share on Facebook"
      >
        <Facebook className={cn(iconSize[size], "fill-current")} />
        {showLabels && <span className="ml-2">Facebook</span>}
      </Button>

      {/* Twitter */}
      <Button
        variant={variant}
        size="icon"
        className={cn(buttonSize[size], "text-[#1DA1F2] hover:bg-[#1DA1F2]/10 border-[#1DA1F2]/20")}
        onClick={() => handleShare('twitter')}
        title="Share on Twitter"
      >
        <Twitter className={cn(iconSize[size], "fill-current")} />
        {showLabels && <span className="ml-2">Twitter</span>}
      </Button>

      {/* LinkedIn */}
      <Button
        variant={variant}
        size="icon"
        className={cn(buttonSize[size], "text-[#0A66C2] hover:bg-[#0A66C2]/10 border-[#0A66C2]/20")}
        onClick={() => handleShare('linkedin')}
        title="Share on LinkedIn"
      >
        <Linkedin className={cn(iconSize[size], "fill-current")} />
        {showLabels && <span className="ml-2">LinkedIn</span>}
      </Button>

      {/* WhatsApp */}
      <Button
        variant={variant}
        size="icon"
        className={cn(buttonSize[size], "text-[#25D366] hover:bg-[#25D366]/10 border-[#25D366]/20")}
        onClick={() => handleShare('whatsapp')}
        title="Share on WhatsApp"
      >
        <MessageCircle className={cn(iconSize[size], "fill-current")} />
        {showLabels && <span className="ml-2">WhatsApp</span>}
      </Button>

      {/* Pinterest */}
      <Button
        variant={variant}
        size="icon"
        className={cn(buttonSize[size], "text-[#E60023] hover:bg-[#E60023]/10 border-[#E60023]/20")}
        onClick={() => handleShare('pinterest')}
        title="Share on Pinterest"
      >
        <PinterestIcon className={iconSize[size]} />
        {showLabels && <span className="ml-2">Pinterest</span>}
      </Button>

      {/* Reddit */}
      <Button
        variant={variant}
        size="icon"
        className={cn(buttonSize[size], "text-[#FF4500] hover:bg-[#FF4500]/10 border-[#FF4500]/20")}
        onClick={() => handleShare('reddit')}
        title="Share on Reddit"
      >
        <RedditIcon className={iconSize[size]} />
        {showLabels && <span className="ml-2">Reddit</span>}
      </Button>

      {/* Copy Link */}
      <Button
        variant={variant}
        size="icon"
        className={cn(
          buttonSize[size],
          copied
            ? "bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20"
            : "text-gray-600 hover:bg-gray-100 border-gray-200"
        )}
        onClick={handleCopyLink}
        title={copied ? "Link copied!" : "Copy link"}
      >
        <Copy className={iconSize[size]} />
        {showLabels && <span className="ml-2">{copied ? "Copied!" : "Copy"}</span>}
      </Button>
    </div>
  );
}
